{% extends "base.html" %}

{% block title %}条件管理 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-filter"></i>
        条件管理
    </h1>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索条件名称...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="fieldFilter">
            <option value="">所有字段</option>
            <option value="title">标题</option>
            <option value="subtitle">副标题</option>
            <option value="ocr_content">OCR内容</option>
            <option value="landing_url">落地页URL</option>
            <option value="advertiser_id">广告主ID</option>
            <option value="industry">行业</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="operatorFilter">
            <option value="">所有操作</option>
            <option value="contains">包含</option>
            <option value="not_contains">不包含</option>
            <option value="equals">等于</option>
            <option value="not_equals">不等于</option>
            <option value="in_wordlist">在词表中</option>
            <option value="not_in_wordlist">不在词表中</option>
        </select>
    </div>
</div>

<!-- 条件列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-list"></i>
            条件列表 ({{ conditions|length }})
        </h6>
    </div>
    <div class="card-body">
        {% if conditions %}
        <div class="table-responsive">
            <table class="table table-hover" id="conditionsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>条件名称</th>
                        <th>字段</th>
                        <th>操作</th>
                        <th>值/词表</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for condition in conditions %}
                    <tr>
                        <td><span class="badge bg-secondary">{{ condition.id }}</span></td>
                        <td><strong>{{ condition.condition_name }}</strong></td>
                        <td>
                            <span class="badge bg-info">
                                {% if condition.field_name == 'title' %}标题
                                {% elif condition.field_name == 'subtitle' %}副标题
                                {% elif condition.field_name == 'ocr_content' %}OCR内容
                                {% elif condition.field_name == 'landing_url' %}落地页URL
                                {% elif condition.field_name == 'advertiser_id' %}广告主ID
                                {% elif condition.field_name == 'industry' %}行业
                                {% else %}{{ condition.field_name }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-warning">
                                {% if condition.operator == 'contains' %}包含
                                {% elif condition.operator == 'not_contains' %}不包含
                                {% elif condition.operator == 'equals' %}等于
                                {% elif condition.operator == 'not_equals' %}不等于
                                {% elif condition.operator == 'in_wordlist' %}在词表中
                                {% elif condition.operator == 'not_in_wordlist' %}不在词表中
                                {% else %}{{ condition.operator }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            {% if condition.wordlist_id %}
                                <span class="badge bg-success">
                                    <i class="fas fa-list"></i>
                                    {{ condition.wordlist_name or '词表' + condition.wordlist_id|string }}
                                </span>
                            {% elif condition.value %}
                                <code>{{ condition.value }}</code>
                            {% else %}
                                <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ condition.created_at or '未知' }}
                            </small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-inbox fa-4x mb-3"></i>
            <h4>暂无条件</h4>
            <p>条件需要通过数据库管理器创建</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 条件详情模态框 -->
<div class="modal fade" id="conditionDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">条件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conditionDetailContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    filterTable();
});

document.getElementById('fieldFilter').addEventListener('change', function() {
    filterTable();
});

document.getElementById('operatorFilter').addEventListener('change', function() {
    filterTable();
});

function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const fieldFilter = document.getElementById('fieldFilter').value;
    const operatorFilter = document.getElementById('operatorFilter').value;
    
    const table = document.getElementById('conditionsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const conditionName = row.cells[1].textContent.toLowerCase();
        const fieldName = row.cells[2].textContent;
        const operator = row.cells[3].textContent;
        
        let showRow = true;
        
        // 搜索过滤
        if (searchTerm && !conditionName.includes(searchTerm)) {
            showRow = false;
        }
        
        // 字段过滤
        if (fieldFilter && !fieldName.includes(fieldFilter)) {
            showRow = false;
        }
        
        // 操作过滤
        if (operatorFilter && !operator.includes(operatorFilter)) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
    }
}

// 点击行显示详情
document.getElementById('conditionsTable').addEventListener('click', function(e) {
    const row = e.target.closest('tr');
    if (row && row.parentNode.tagName === 'TBODY') {
        showConditionDetail(row);
    }
});

function showConditionDetail(row) {
    const cells = row.cells;
    const conditionId = cells[0].textContent.trim();
    const conditionName = cells[1].textContent.trim();
    const fieldName = cells[2].textContent.trim();
    const operator = cells[3].textContent.trim();
    const value = cells[4].textContent.trim();
    const createdAt = cells[5].textContent.trim();
    
    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${conditionId}</td></tr>
                    <tr><td><strong>名称:</strong></td><td>${conditionName}</td></tr>
                    <tr><td><strong>创建时间:</strong></td><td>${createdAt}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>条件配置</h6>
                <table class="table table-sm">
                    <tr><td><strong>字段:</strong></td><td>${fieldName}</td></tr>
                    <tr><td><strong>操作:</strong></td><td>${operator}</td></tr>
                    <tr><td><strong>值/词表:</strong></td><td>${value}</td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>条件说明</h6>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    当广告的 <strong>${fieldName}</strong> 字段 <strong>${operator}</strong> <code>${value}</code> 时，此条件为真。
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('conditionDetailContent').innerHTML = detailHtml;
    $('#conditionDetailModal').modal('show');
}
</script>
{% endblock %}
