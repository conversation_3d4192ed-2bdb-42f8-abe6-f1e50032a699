# 机审规则引擎

由词表+条件+规则，组成最终的规则

支持维护词表，并将词表嵌入条件，如 标题内容包含词表中任意一个词


支持以下形式的条件
- 入审的某文本或OCR字段，包含或不包含某文本；(如 副标题内容包含"零元购"、图片OCR内容不包含"投资有风险")
- 属于或不属于某行业
- 广告主ID是否等于某值
- 落地页URL是否包含或不包含某文本


支持以下形式的规则
- 同时符合多个条件或规则
- 符合多个条件或规则其中之一

## 数据库设计

使用SQLAlchemy + SQLite，包含三类表：

### 词表相关表
- `wordlist` - 词表主表（存储词表名称和描述）
- `wordlist_items` - 词表项（存储具体的词汇）

### 条件表 (conditions)
- `id` - 条件ID（主键）
- `condition_name` - 条件名称
- `field_name` - 广告信息字段（枚举：title, subtitle, ocr_content, landing_url, advertiser_id, industry）
- `operator` - 条件运算（枚举：contains, not_contains, equals, not_equals, in_wordlist, not_in_wordlist）
- `value` - 限制值（当使用词表时为空）
- `wordlist_id` - 词表ID（当operator为词表相关操作时使用）

### 规则表 (rules)
- `id` - 规则ID（主键）
- `rule_name` - 规则名称
- `rule_description` - 规则详细描述
- `logic_expression` - 逻辑表达式（如：(1 AND 2) OR (3 AND 4)）
- `status` - 规则状态（枚举：active, inactive）
- `priority` - 规则优先级（数字越大优先级越高）

## 快速开始

### 1. 安装依赖
```bash
pip install sqlalchemy
```

### 2. 初始化数据库
```python
from database_utils import db_manager

# 创建数据库表
db_manager.create_tables()
```

### 3. 创建词表并添加词汇
```python
# 创建词表
wordlist_id = db_manager.create_wordlist("黑名单词表", "包含违规词汇的词表")

# 添加词汇
black_words = ["零元购", "免费领取", "限时抢购"]
db_manager.add_words_to_wordlist(wordlist_id, black_words)
```

### 4. 创建条件
```python
# 创建条件：标题包含黑名单词汇
condition_id = db_manager.create_condition(
    condition_name="标题包含黑名单词汇",
    field_name="title",
    operator="in_wordlist",
    wordlist_id=wordlist_id
)
```

### 5. 创建规则
```python
# 创建规则
rule_id = db_manager.create_rule(
    rule_name="高风险广告检测",
    rule_description="检测包含黑名单词汇的广告",
    logic_expression=f"{condition_id}",
    priority=10
)
```

### 6. 使用规则引擎进行审核
```python
from demo_input import CreativeInfo
from rule_engine_main import audit_creative_info

# 创建创意信息
creative_info = CreativeInfo(
    creative_id=1001,
    industry="金融",
    title="零元购买理财产品",
    subtitle="高收益保证",
    image_url="http://example.com/image.jpg",
    image_ocr="投资理财广告",
    video_id=0,
    video_ocr=[],
    ldp_url="http://example.com/landing",
    device_type="mobile",
    advertiser_id=12345,
    license_info=[],
    request_id="req_001"
)

# 执行审核
result = audit_creative_info(creative_info)

# 查看结果
print(f"是否命中规则: {result.is_hit}")
print(f"命中规则数量: {len(result.hit_rules)}")
for hit_rule in result.hit_rules:
    print(f"规则: {hit_rule.rule_name} (ID: {hit_rule.rule_id})")
```

### 7. 运行示例
```bash
# 数据库操作示例
python usage_example.py

# 规则引擎使用示例
python rule_engine_example.py
```

## 文件结构

```
机审规则引擎/
├── README.md                    # 项目说明文档
├── config.py                    # 配置文件
├── requirements.txt             # 依赖包列表
├── demo_input.py               # 数据结构定义
├── rule_engine_main.py         # 规则引擎主处理模块
├── rule_engine_example.py      # 规则引擎使用示例
├── usage_example.py            # 数据库使用示例
├── database_utils/             # 数据库工具包
│   ├── __init__.py            # 包初始化文件
│   ├── models.py              # 数据库模型定义
│   ├── database_manager.py    # 数据库管理器
│   ├── database_example.py    # 数据库操作示例
│   └── database/              # 数据库文件存储目录
│       └── rule_engine.db     # SQLite数据库文件
└── logs/                       # 日志文件目录
```

## 规则引擎功能

### 核心组件

1. **RuleEngine类** - 主要的规则引擎处理器
2. **AuditResult类** - 审核结果数据结构
3. **RuleHitInfo类** - 规则命中信息数据结构

### 支持的字段类型

- `title` - 标题
- `subtitle` - 副标题
- `image_ocr` - 图片OCR内容
- `ldp_url` - 落地页URL
- `advertiser_id` - 广告主ID
- `industry` - 行业

### 支持的操作类型

- `contains` - 包含
- `not_contains` - 不包含
- `equals` - 等于
- `not_equals` - 不等于
- `in_wordlist` - 在词表中
- `not_in_wordlist` - 不在词表中

### 审核结果

审核结果包含以下信息：
- 是否命中任何规则
- 命中的规则列表（包含规则ID、名称、描述、优先级）
- 命中的具体条件详情
- 审核时间
- 检查的规则总数