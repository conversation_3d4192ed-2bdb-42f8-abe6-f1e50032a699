"""
数据库操作示例
演示如何使用定义的模型进行CRUD操作
"""

from models import (
    create_database, get_session,
    BlackWordlist, WhiteWordlist, SensitiveWordlist,
    Condition, Rule,
    FieldType, OperatorType, RuleStatus
)

def init_sample_data():
    """初始化示例数据"""
    engine = create_database()
    session = get_session(engine)

    try:
        # 1. 添加词表数据
        print("添加词表数据...")

        # 黑名单词表
        black_words = ["零元购", "免费领取", "限时抢购", "投资理财"]
        for word in black_words:
            black_word = BlackWordlist(word=word)
            session.add(black_word)

        # 白名单词表
        white_words = ["官方", "正品", "品牌授权"]
        for word in white_words:
            white_word = WhiteWordlist(word=word)
            session.add(white_word)

        # 敏感词表
        sensitive_words = ["投资有风险", "收益不保证"]
        for word in sensitive_words:
            sensitive_word = SensitiveWordlist(word=word)
            session.add(sensitive_word)

        session.commit()
        print("词表数据添加完成")

        # 2. 添加条件数据
        print("添加条件数据...")

        # 条件1：标题包含黑名单词汇
        condition1 = Condition(
            condition_name="标题包含黑名单词汇",
            field_name=FieldType.TITLE,
            operator=OperatorType.IN_WORDLIST,
            wordlist_type="black_wordlist"
        )
        session.add(condition1)

        # 条件2：副标题不包含"投资有风险"
        condition2 = Condition(
            condition_name="副标题不包含风险提示",
            field_name=FieldType.SUBTITLE,
            operator=OperatorType.NOT_CONTAINS,
            value="投资有风险"
        )
        session.add(condition2)

        # 条件3：广告主ID等于特定值
        condition3 = Condition(
            condition_name="特定广告主",
            field_name=FieldType.ADVERTISER_ID,
            operator=OperatorType.EQUALS,
            value="12345"
        )
        session.add(condition3)

        # 条件4：落地页URL包含可疑域名
        condition4 = Condition(
            condition_name="可疑域名检查",
            field_name=FieldType.LANDING_URL,
            operator=OperatorType.CONTAINS,
            value="suspicious-domain.com"
        )
        session.add(condition4)

        session.commit()
        print("条件数据添加完成")

        # 3. 添加规则数据
        print("添加规则数据...")

        # 规则1：高风险广告检测
        rule1 = Rule(
            rule_name="高风险广告检测",
            rule_description="检测包含黑名单词汇且缺少风险提示的广告",
            logic_expression="(1 AND 2)",  # 条件1 AND 条件2
            status=RuleStatus.ACTIVE,
            priority=10
        )
        session.add(rule1)

        # 规则2：特定广告主监控
        rule2 = Rule(
            rule_name="特定广告主监控",
            rule_description="监控特定广告主或可疑域名的广告",
            logic_expression="3 OR 4",  # 条件3 OR 条件4
            status=RuleStatus.ACTIVE,
            priority=5
        )
        session.add(rule2)

        # 规则3：综合风险评估
        rule3 = Rule(
            rule_name="综合风险评估",
            rule_description="综合多个条件进行风险评估",
            logic_expression="(1 AND 2) OR (3 AND 4)",  # (条件1 AND 条件2) OR (条件3 AND 条件4)
            status=RuleStatus.ACTIVE,
            priority=8
        )
        session.add(rule3)

        session.commit()
        print("规则数据添加完成")

        print("示例数据初始化完成！")

    except Exception as e:
        session.rollback()
        print(f"数据初始化失败: {e}")
    finally:
        session.close()

def query_examples():
    """查询示例"""
    engine = create_database()
    session = get_session(engine)

    try:
        print("\n=== 查询示例 ===")

        # 查询所有黑名单词汇
        print("\n1. 黑名单词汇:")
        black_words = session.query(BlackWordlist).all()
        for word in black_words:
            print(f"  - {word.word}")

        # 查询所有启用的规则
        print("\n2. 启用的规则:")
        active_rules = session.query(Rule).filter(Rule.status == RuleStatus.ACTIVE).order_by(Rule.priority.desc()).all()
        for rule in active_rules:
            print(f"  - {rule.rule_name} (优先级: {rule.priority})")
            print(f"    逻辑表达式: {rule.logic_expression}")

        # 查询特定字段的条件
        print("\n3. 标题相关的条件:")
        title_conditions = session.query(Condition).filter(Condition.field_name == FieldType.TITLE).all()
        for condition in title_conditions:
            print(f"  - {condition.condition_name}: {condition.operator.value}")

        # 查询使用词表的条件
        print("\n4. 使用词表的条件:")
        wordlist_conditions = session.query(Condition).filter(Condition.wordlist_type.isnot(None)).all()
        for condition in wordlist_conditions:
            print(f"  - {condition.condition_name}: 使用 {condition.wordlist_type}")

    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    # 初始化示例数据
    init_sample_data()

    # 执行查询示例
    query_examples()
