# 规则引擎Web UI

基于FastAPI的规则引擎可视化管理界面，提供直观的规则管理和测试功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install fastapi uvicorn jinja2 python-multipart
```

### 2. 启动服务

```bash
# 方法1: 使用启动脚本（推荐）
python start_ui.py

# 方法2: 直接使用uvicorn
uvicorn database_utils.ui.main:app --host 0.0.0.0 --port 8000 --reload

# 方法3: 运行main.py
python database_utils/ui/main.py
```

### 3. 访问界面

- **主界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **交互式API**: http://localhost:8000/redoc

## 📱 功能特性

### 🏠 概览页面 (/)
- **系统统计**: 词表数量、词汇总数、条件数量、活跃规则数
- **最近数据**: 显示最近创建的词表和规则
- **快速操作**: 一键跳转到各个管理页面

### 📝 词表管理 (/wordlists)
- **词表列表**: 查看所有词表及其基本信息
- **创建词表**: 支持创建新的词表并添加描述
- **查看词汇**: 查看词表中的所有词汇
- **添加词汇**: 批量添加词汇到指定词表
- **删除词表**: 删除不需要的词表

### 🔍 条件管理 (/conditions)
- **条件列表**: 查看所有条件的详细信息
- **搜索筛选**: 按条件名称、字段类型、操作类型筛选
- **条件详情**: 点击条件查看详细配置信息
- **字段映射**: 直观显示字段类型和操作类型

### ⚙️ 规则管理 (/rules)
- **规则列表**: 查看所有规则及其状态
- **优先级显示**: 按优先级高低用不同颜色标识
- **规则详情**: 查看规则的逻辑表达式和描述
- **状态切换**: 启用/禁用规则
- **搜索筛选**: 按规则名称、描述、优先级筛选

### 🧪 测试工具 (/test)
- **创意测试**: 输入创意信息进行实时审核测试
- **快速案例**: 预设的测试案例（正常广告、违规广告等）
- **结果展示**: 详细显示审核结果和命中的规则
- **JSON查看**: 查看完整的JSON格式测试结果

## 🎨 界面设计

### 响应式设计
- 支持桌面端和移动端访问
- Bootstrap 5.1.3 框架
- 现代化的渐变色设计
- Font Awesome 图标

### 用户体验
- 直观的侧边栏导航
- 实时搜索和筛选
- 模态框操作
- 加载状态提示
- 错误处理和用户反馈

## 🔧 API接口

### 统计接口
- `GET /api/stats` - 获取系统统计数据

### 词表接口
- `POST /api/wordlists` - 创建词表
- `GET /wordlists/{wordlist_id}/words` - 获取词表词汇
- `POST /api/wordlists/{wordlist_id}/words` - 添加词汇
- `DELETE /api/wordlists/{wordlist_id}` - 删除词表

### 规则接口
- `PUT /api/rules/{rule_id}/status` - 更新规则状态

### 测试接口
- `POST /api/test-creative` - 测试创意审核

## 📊 数据展示

### 统计卡片
- 词表数量统计
- 总词汇数统计
- 条件数量统计
- 活跃规则数统计

### 表格展示
- 分页显示（前端实现）
- 排序功能
- 搜索筛选
- 响应式表格

### 状态标识
- 规则状态：启用/禁用
- 优先级：高/中/低
- 字段类型：不同颜色标识
- 操作类型：徽章显示

## 🛠️ 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **Uvicorn**: ASGI服务器
- **Jinja2**: 模板引擎
- **SQLAlchemy**: 数据库ORM

### 前端
- **Bootstrap 5**: CSS框架
- **jQuery**: JavaScript库
- **Font Awesome**: 图标库
- **原生JavaScript**: 交互逻辑

## 🔒 安全考虑

### 输入验证
- 表单数据验证
- SQL注入防护（SQLAlchemy ORM）
- XSS防护（模板转义）

### 错误处理
- 统一的错误响应格式
- 用户友好的错误提示
- 服务器错误日志记录

## 📈 性能优化

### 前端优化
- CDN资源加载
- 异步请求
- 前端缓存

### 后端优化
- 数据库连接池
- 查询优化
- 响应压缩

## 🚀 部署建议

### 开发环境
```bash
uvicorn database_utils.ui.main:app --host 0.0.0.0 --port 8000 --reload
```

### 生产环境
```bash
uvicorn database_utils.ui.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Docker部署
```dockerfile
FROM python:3.10-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "database_utils.ui.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🔄 未来计划

### 功能扩展
- [ ] Excel导出功能
- [ ] 规则可视化图表
- [ ] 批量操作功能
- [ ] 用户权限管理
- [ ] 操作日志记录

### 性能优化
- [ ] 数据分页
- [ ] 缓存机制
- [ ] 异步处理
- [ ] 数据库索引优化

### 用户体验
- [ ] 拖拽排序
- [ ] 快捷键支持
- [ ] 主题切换
- [ ] 多语言支持

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :8000
   # 更换端口
   uvicorn database_utils.ui.main:app --port 8001
   ```

2. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

3. **数据库连接失败**
   - 检查数据库文件路径
   - 确保有读写权限
   - 检查SQLAlchemy配置

4. **模板文件找不到**
   - 确保templates目录存在
   - 检查文件路径配置
   - 验证模板文件完整性

### 调试模式
```bash
# 启用调试模式
uvicorn database_utils.ui.main:app --reload --log-level debug
```

这个Web UI为规则引擎提供了完整的可视化管理界面，让规则管理变得简单直观！
