"""
规则引擎Web UI主应用
基于FastAPI实现的可视化界面
"""

from fastapi import FastAPI, Request, HTTPException, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pathlib import Path
import sys
import os
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from database_utils import db_manager
from demo_input import CreativeInfo
from rule_engine_main import audit_creative_info

# 创建FastAPI应用
app = FastAPI(
    title="规则引擎管理系统",
    description="广告审核规则引擎的可视化管理界面",
    version="1.0.0"
)

# 设置模板和静态文件
templates = Jinja2Templates(directory=str(current_dir / "templates"))
app.mount("/static", StaticFiles(directory=str(current_dir / "static")), name="static")


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """首页 - 规则引擎概览"""
    try:
        # 获取统计数据
        wordlists = db_manager.get_all_wordlists()
        conditions = db_manager.get_all_conditions()
        rules = db_manager.get_active_rules()

        stats = {
            "wordlist_count": len(wordlists),
            "condition_count": len(conditions),
            "active_rule_count": len(rules),
            "total_words": sum(wl.get('word_count', 0) for wl in wordlists)
        }

        return templates.TemplateResponse(
            "dashboard.html",
            {
                "request": request,
                "stats": stats,
                "recent_wordlists": wordlists[:5],
                "recent_rules": rules[:5]
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")


@app.get("/wordlists", response_class=HTMLResponse)
async def wordlists_page(request: Request):
    """词表管理页面"""
    try:
        wordlists = db_manager.get_all_wordlists()
        return templates.TemplateResponse(
            "wordlists.html",
            {"request": request, "wordlists": wordlists}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取词表失败: {str(e)}")


@app.get("/wordlists/{wordlist_id}/words")
async def get_wordlist_words(wordlist_id: int):
    """获取指定词表的词汇"""
    try:
        words = db_manager.get_wordlist(wordlist_id)
        return {"words": words}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取词汇失败: {str(e)}")


@app.get("/conditions", response_class=HTMLResponse)
async def conditions_page(request: Request):
    """条件管理页面"""
    try:
        conditions = db_manager.get_all_conditions()
        return templates.TemplateResponse(
            "conditions.html",
            {"request": request, "conditions": conditions}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取条件失败: {str(e)}")


@app.get("/rules", response_class=HTMLResponse)
async def rules_page(request: Request):
    """规则管理页面"""
    try:
        rules = db_manager.get_active_rules()
        return templates.TemplateResponse(
            "rules.html",
            {"request": request, "rules": rules}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取规则失败: {str(e)}")


@app.get("/test", response_class=HTMLResponse)
async def test_page(request: Request):
    """测试页面"""
    return templates.TemplateResponse("test.html", {"request": request})


@app.post("/api/test-creative")
async def test_creative(
    creative_id: int = Form(...),
    industry: str = Form(...),
    title: str = Form(...),
    subtitle: str = Form(""),
    image_ocr: str = Form(""),
    ldp_url: str = Form(""),
    advertiser_id: int = Form(0),
    request_id: str = Form("web_test")
):
    """测试创意审核"""
    try:
        # 创建创意信息对象
        creative_info = CreativeInfo(
            creative_id=creative_id,
            industry=industry,
            title=title,
            subtitle=subtitle,
            image_url="",
            image_ocr=image_ocr,
            video_id=0,
            video_ocr=[],
            ldp_url=ldp_url,
            device_type="web",
            advertiser_id=advertiser_id,
            license_info=[],
            request_id=request_id
        )

        # 执行审核
        result = audit_creative_info(creative_info)

        return {
            "success": True,
            "result": result.to_dict()
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@app.get("/api/stats")
async def get_stats():
    """获取统计数据API"""
    try:
        wordlists = db_manager.get_all_wordlists()
        conditions = db_manager.get_all_conditions()
        rules = db_manager.get_active_rules()

        return {
            "wordlist_count": len(wordlists),
            "condition_count": len(conditions),
            "active_rule_count": len(rules),
            "total_words": sum(wl.get('word_count', 0) for wl in wordlists)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/wordlists")
async def create_wordlist(name: str = Form(...), description: str = Form("")):
    """创建新词表"""
    try:
        wordlist_id = db_manager.create_wordlist(name, description)
        return {"success": True, "wordlist_id": wordlist_id}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/api/wordlists/{wordlist_id}/words")
async def add_words_to_wordlist(wordlist_id: int, words: str = Form(...)):
    """向词表添加词汇"""
    try:
        word_list = [word.strip() for word in words.split('\n') if word.strip()]
        db_manager.add_words_to_wordlist(wordlist_id, word_list)
        return {"success": True, "added_count": len(word_list)}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.delete("/api/wordlists/{wordlist_id}")
async def delete_wordlist(wordlist_id: int):
    """删除词表"""
    try:
        success = db_manager.delete_wordlist(wordlist_id)
        return {"success": success}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.put("/api/rules/{rule_id}/status")
async def update_rule_status(rule_id: int, status: str = Form(...)):
    """更新规则状态"""
    try:
        success = db_manager.update_rule_status(rule_id, status)
        return {"success": success}
    except Exception as e:
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("database_utils.ui.main:app", host="0.0.0.0", port=8000, reload=True)
