"""
数据库管理工具类
提供便捷的数据库操作接口
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from typing import List, Optional, Dict, Any

from models import (
    Base, Wordlist, WordlistItem,
    Condition, Rule, FieldType, OperatorType, RuleStatus
)
from config import DATABASE_CONFIG

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_url: str = None):
        self.db_url = db_url or DATABASE_CONFIG['url']
        self.engine = create_engine(self.db_url, echo=DATABASE_CONFIG['echo'])
        self.SessionLocal = sessionmaker(bind=self.engine)

    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(self.engine)

    @contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    # 词表操作
    def create_wordlist(self, name: str, description: str = None) -> int:
        """创建新词表"""
        with self.get_session() as session:
            wordlist = Wordlist(name=name, description=description)
            session.add(wordlist)
            session.flush()
            return wordlist.id

    def add_words_to_wordlist(self, wordlist_id: int, words: List[str]) -> bool:
        """批量添加词汇到指定词表"""
        with self.get_session() as session:
            # 检查词表是否存在
            wordlist = session.query(Wordlist).filter(Wordlist.id == wordlist_id).first()
            if not wordlist:
                raise ValueError(f"词表ID {wordlist_id} 不存在")

            for word in words:
                # 检查词汇是否已存在
                existing = session.query(WordlistItem).filter(
                    WordlistItem.wordlist_id == wordlist_id,
                    WordlistItem.word == word
                ).first()
                if not existing:
                    session.add(WordlistItem(wordlist_id=wordlist_id, word=word))
        return True

    def remove_words_from_wordlist(self, wordlist_id: int, words: List[str]) -> bool:
        """从指定词表中删除词汇"""
        with self.get_session() as session:
            for word in words:
                session.query(WordlistItem).filter(
                    WordlistItem.wordlist_id == wordlist_id,
                    WordlistItem.word == word
                ).delete()
        return True

    def get_wordlist(self, wordlist_id: int) -> List[str]:
        """获取指定词表的所有词汇"""
        with self.get_session() as session:
            items = session.query(WordlistItem).filter(WordlistItem.wordlist_id == wordlist_id).all()
            return [item.word for item in items]

    def get_all_wordlists(self) -> List[Dict[str, Any]]:
        """获取所有词表"""
        with self.get_session() as session:
            wordlists = session.query(Wordlist).all()
            return [
                {
                    'id': wordlist.id,
                    'name': wordlist.name,
                    'description': wordlist.description,
                    'word_count': len(wordlist.items)
                }
                for wordlist in wordlists
            ]

    def delete_wordlist(self, wordlist_id: int) -> bool:
        """删除词表及其所有词汇"""
        with self.get_session() as session:
            # 先删除词表项
            session.query(WordlistItem).filter(WordlistItem.wordlist_id == wordlist_id).delete()
            # 再删除词表
            result = session.query(Wordlist).filter(Wordlist.id == wordlist_id).delete()
            return result > 0

    # 条件操作
    def create_condition(self, condition_name: str, field_name: str, operator: str,
                        value: str = None, wordlist_id: int = None) -> int:
        """创建新条件"""
        with self.get_session() as session:
            condition = Condition(
                condition_name=condition_name,
                field_name=FieldType(field_name),
                operator=OperatorType(operator),
                value=value,
                wordlist_id=wordlist_id
            )
            session.add(condition)
            session.flush()  # 获取ID
            return condition.id

    def get_condition(self, condition_id: int) -> Optional[Dict[str, Any]]:
        """获取指定条件"""
        with self.get_session() as session:
            condition = session.query(Condition).filter(Condition.id == condition_id).first()
            if condition:
                return {
                    'id': condition.id,
                    'condition_name': condition.condition_name,
                    'field_name': condition.field_name.value,
                    'operator': condition.operator.value,
                    'value': condition.value,
                    'wordlist_id': condition.wordlist_id,
                    'wordlist_name': condition.wordlist.name if condition.wordlist else None
                }
            return None

    def get_all_conditions(self) -> List[Dict[str, Any]]:
        """获取所有条件"""
        with self.get_session() as session:
            conditions = session.query(Condition).all()
            return [
                {
                    'id': condition.id,
                    'condition_name': condition.condition_name,
                    'field_name': condition.field_name.value,
                    'operator': condition.operator.value,
                    'value': condition.value,
                    'wordlist_id': condition.wordlist_id,
                    'wordlist_name': condition.wordlist.name if condition.wordlist else None
                }
                for condition in conditions
            ]

    def delete_condition(self, condition_id: int) -> bool:
        """删除条件"""
        with self.get_session() as session:
            result = session.query(Condition).filter(Condition.id == condition_id).delete()
            return result > 0

    # 规则操作
    def create_rule(self, rule_name: str, logic_expression: str,
                   rule_description: str = None, priority: int = 0) -> int:
        """创建新规则"""
        with self.get_session() as session:
            rule = Rule(
                rule_name=rule_name,
                rule_description=rule_description,
                logic_expression=logic_expression,
                priority=priority,
                status=RuleStatus.ACTIVE
            )
            session.add(rule)
            session.flush()
            return rule.id

    def get_rule(self, rule_id: int) -> Optional[Dict[str, Any]]:
        """获取指定规则"""
        with self.get_session() as session:
            rule = session.query(Rule).filter(Rule.id == rule_id).first()
            if rule:
                return {
                    'id': rule.id,
                    'rule_name': rule.rule_name,
                    'rule_description': rule.rule_description,
                    'logic_expression': rule.logic_expression,
                    'status': rule.status.value,
                    'priority': rule.priority
                }
            return None

    def get_active_rules(self) -> List[Dict[str, Any]]:
        """获取所有启用的规则，按优先级排序"""
        with self.get_session() as session:
            rules = session.query(Rule).filter(
                Rule.status == RuleStatus.ACTIVE
            ).order_by(Rule.priority.desc()).all()

            return [
                {
                    'id': rule.id,
                    'rule_name': rule.rule_name,
                    'rule_description': rule.rule_description,
                    'logic_expression': rule.logic_expression,
                    'status': rule.status.value,
                    'priority': rule.priority
                }
                for rule in rules
            ]

    def update_rule_status(self, rule_id: int, status: str) -> bool:
        """更新规则状态"""
        with self.get_session() as session:
            rule = session.query(Rule).filter(Rule.id == rule_id).first()
            if rule:
                rule.status = RuleStatus(status)
                return True
            return False

    def delete_rule(self, rule_id: int) -> bool:
        """删除规则"""
        with self.get_session() as session:
            result = session.query(Rule).filter(Rule.id == rule_id).delete()
            return result > 0

# 全局数据库管理器实例
db_manager = DatabaseManager()
