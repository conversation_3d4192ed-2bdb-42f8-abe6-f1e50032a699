"""
测试新功能：规则动作和优先级逻辑
"""

from demo_input import CreativeInfo
from rule_engine_main import audit_creative_info

def test_rule_actions():
    """测试规则动作和优先级逻辑"""
    print("🧪 测试新的规则动作和优先级逻辑")
    print("=" * 60)
    
    # 测试案例1：违规词 - 应该被最高优先级拒绝规则拦截
    print("\n📝 测试案例1：包含违规词的广告")
    creative1 = CreativeInfo(
        creative_id=1001,
        industry="电商",
        title="免费领取商品",  # 包含违规词"免费"
        subtitle="投资有风险，理财需谨慎",
        image_url="",
        image_ocr="",
        video_id=0,
        video_ocr=[],
        ldp_url="https://example.com",
        device_type="mobile",
        advertiser_id=67890,  # 白名单广告主
        license_info=[],
        request_id="test_001"
    )
    
    result1 = audit_creative_info(creative1)
    print(f"结果: {result1.final_decision}")
    print(f"命中规则数: {len(result1.hit_rules)}")
    for rule in result1.hit_rules:
        print(f"  - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
    
    # 测试案例2：白名单广告主 - 应该被高优先级通过规则放行
    print("\n📝 测试案例2：白名单广告主的普通广告")
    creative2 = CreativeInfo(
        creative_id=1002,
        industry="金融",  # 金融行业
        title="投资理财产品",
        subtitle="",  # 没有风险提示
        image_url="",
        image_ocr="",
        video_id=0,
        video_ocr=[],
        ldp_url="https://example.com",
        device_type="mobile",
        advertiser_id=67890,  # 白名单广告主
        license_info=[],
        request_id="test_002"
    )
    
    result2 = audit_creative_info(creative2)
    print(f"结果: {result2.final_decision}")
    print(f"命中规则数: {len(result2.hit_rules)}")
    for rule in result2.hit_rules:
        print(f"  - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
    
    # 测试案例3：金融广告无风险提示 - 应该被拒绝
    print("\n📝 测试案例3：金融广告无风险提示")
    creative3 = CreativeInfo(
        creative_id=1003,
        industry="金融",
        title="投资理财产品",
        subtitle="",  # 没有风险提示
        image_url="",
        image_ocr="",
        video_id=0,
        video_ocr=[],
        ldp_url="https://example.com",
        device_type="mobile",
        advertiser_id=12345,  # 非白名单广告主
        license_info=[],
        request_id="test_003"
    )
    
    result3 = audit_creative_info(creative3)
    print(f"结果: {result3.final_decision}")
    print(f"命中规则数: {len(result3.hit_rules)}")
    for rule in result3.hit_rules:
        print(f"  - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
    
    # 测试案例4：合规金融广告 - 应该通过
    print("\n📝 测试案例4：合规金融广告")
    creative4 = CreativeInfo(
        creative_id=1004,
        industry="金融",
        title="投资理财产品",
        subtitle="投资有风险，理财需谨慎",  # 包含风险提示
        image_url="",
        image_ocr="",
        video_id=0,
        video_ocr=[],
        ldp_url="https://example.com",
        device_type="mobile",
        advertiser_id=12345,  # 非白名单广告主
        license_info=[],
        request_id="test_004"
    )
    
    result4 = audit_creative_info(creative4)
    print(f"结果: {result4.final_decision}")
    print(f"命中规则数: {len(result4.hit_rules)}")
    for rule in result4.hit_rules:
        print(f"  - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
    
    # 测试案例5：普通广告 - 应该通过
    print("\n📝 测试案例5：普通广告")
    creative5 = CreativeInfo(
        creative_id=1005,
        industry="电商",
        title="品牌官方旗舰店",
        subtitle="正品保证",
        image_url="",
        image_ocr="",
        video_id=0,
        video_ocr=[],
        ldp_url="https://example.com",
        device_type="mobile",
        advertiser_id=12345,
        license_info=[],
        request_id="test_005"
    )
    
    result5 = audit_creative_info(creative5)
    print(f"结果: {result5.final_decision}")
    print(f"命中规则数: {len(result5.hit_rules)}")
    for rule in result5.hit_rules:
        print(f"  - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")

if __name__ == "__main__":
    test_rule_actions()
