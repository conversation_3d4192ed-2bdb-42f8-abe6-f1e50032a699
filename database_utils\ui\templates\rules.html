{% extends "base.html" %}

{% block title %}规则管理 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-sitemap"></i>
        规则管理
    </h1>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索规则名称或描述...">
        </div>
    </div>
    <div class="col-md-4">
        <select class="form-select" id="priorityFilter">
            <option value="">所有优先级</option>
            <option value="high">高优先级 (≥8)</option>
            <option value="medium">中优先级 (4-7)</option>
            <option value="low">低优先级 (≤3)</option>
        </select>
    </div>
</div>

<!-- 规则列表 -->
<div class="row">
    {% for rule in rules %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">{{ rule.rule_name }}</h6>
                <div class="d-flex align-items-center">
                    <span class="badge bg-{% if rule.priority >= 8 %}danger{% elif rule.priority >= 4 %}warning{% else %}secondary{% endif %} me-2">
                        优先级 {{ rule.priority }}
                    </span>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="viewRuleDetail({{ rule.id }})">
                                <i class="fas fa-eye"></i> 查看详情
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="toggleRuleStatus({{ rule.id }}, '{{ rule.status }}')">
                                <i class="fas fa-{% if rule.status == 'active' %}pause{% else %}play{% endif %}"></i>
                                {% if rule.status == 'active' %}禁用{% else %}启用{% endif %}
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-text">{{ rule.rule_description or '无描述' }}</p>

                <div class="mb-3">
                    <small class="text-muted">逻辑表达式:</small>
                    <div class="mt-1">
                        <code class="logic-expression">{{ rule.logic_expression }}</code>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-muted">动作</small>
                    <span class="badge bg-{% if rule.action == 'approve' %}success{% else %}danger{% endif %}">
                        <i class="fas fa-{% if rule.action == 'approve' %}check{% else %}times{% endif %}"></i>
                        {% if rule.action == 'approve' %}通过{% else %}拒绝{% endif %}
                    </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">状态</small>
                    <span class="badge bg-{% if rule.status == 'active' %}success{% else %}secondary{% endif %}">
                        <i class="fas fa-{% if rule.status == 'active' %}check{% else %}pause{% endif %}"></i>
                        {% if rule.status == 'active' %}启用{% else %}禁用{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    {% if not rules %}
    <div class="col-12">
        <div class="text-center text-muted py-5">
            <i class="fas fa-inbox fa-4x mb-3"></i>
            <h4>暂无规则</h4>
            <p>规则需要通过数据库管理器创建</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">规则详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.logic-expression {
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    word-break: break-all;
}

.condition-badge {
    margin: 0.125rem;
    font-size: 0.75rem;
}

.rule-flow {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    filterRules();
});

document.getElementById('priorityFilter').addEventListener('change', function() {
    filterRules();
});

function filterRules() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const priorityFilter = document.getElementById('priorityFilter').value;

    const cards = document.querySelectorAll('.col-lg-6');

    cards.forEach(card => {
        const ruleName = card.querySelector('.card-header h6').textContent.toLowerCase();
        const ruleDesc = card.querySelector('.card-text').textContent.toLowerCase();
        const priorityBadge = card.querySelector('.badge');
        const priority = parseInt(priorityBadge.textContent.match(/\d+/)[0]);

        let showCard = true;

        // 搜索过滤
        if (searchTerm && !ruleName.includes(searchTerm) && !ruleDesc.includes(searchTerm)) {
            showCard = false;
        }

        // 优先级过滤
        if (priorityFilter) {
            if (priorityFilter === 'high' && priority < 8) showCard = false;
            if (priorityFilter === 'medium' && (priority < 4 || priority > 7)) showCard = false;
            if (priorityFilter === 'low' && priority > 3) showCard = false;
        }

        card.style.display = showCard ? '' : 'none';
    });
}

// 查看规则详情
function viewRuleDetail(ruleId) {
    $('#ruleDetailModal').modal('show');

    // 这里可以通过API获取更详细的规则信息
    // 暂时显示基本信息
    const ruleCard = document.querySelector(`[onclick="viewRuleDetail(${ruleId})"]`).closest('.card');
    const ruleName = ruleCard.querySelector('.card-header h6').textContent;
    const ruleDesc = ruleCard.querySelector('.card-text').textContent;
    const logicExpr = ruleCard.querySelector('.logic-expression').textContent;
    const priority = ruleCard.querySelector('.badge').textContent;
    const badges = ruleCard.querySelectorAll('.card-body .badge');
    const action = badges[0].textContent;
    const status = badges[1].textContent;

    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${ruleId}</td></tr>
                    <tr><td><strong>名称:</strong></td><td>${ruleName}</td></tr>
                    <tr><td><strong>描述:</strong></td><td>${ruleDesc}</td></tr>
                    <tr><td><strong>优先级:</strong></td><td>${priority}</td></tr>
                    <tr><td><strong>动作:</strong></td><td>${action}</td></tr>
                    <tr><td><strong>状态:</strong></td><td>${status}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>逻辑表达式</h6>
                <div class="rule-flow">
                    <code>${logicExpr}</code>
                </div>
                <small class="text-muted mt-2 d-block">
                    <i class="fas fa-info-circle"></i>
                    数字代表条件ID，AND/OR表示逻辑关系
                </small>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h6>规则说明</h6>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    此规则会根据逻辑表达式 <code>${logicExpr}</code> 来判断广告是否符合条件。
                    当表达式结果为真时，广告将被此规则命中。
                </div>
            </div>
        </div>
    `;

    document.getElementById('ruleDetailContent').innerHTML = detailHtml;
}

// 切换规则状态
function toggleRuleStatus(ruleId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? '启用' : '禁用';

    if (confirm(`确定要${action}这个规则吗？`)) {
        const formData = new FormData();
        formData.append('status', newStatus);

        fetch(`/api/rules/${ruleId}/status`, {
            method: 'PUT',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(`${action}失败: ` + data.error);
            }
        })
        .catch(error => {
            alert(`${action}失败: ` + error);
        });
    }
}
</script>
{% endblock %}
