"""
机审规则引擎主处理模块
负责处理广告审核请求，执行规则匹配和判断
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from demo_input import CreativeInfo
from database_utils import db_manager, FieldType, OperatorType, RuleAction

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RuleHitInfo:
    """规则命中信息"""
    rule_id: int
    rule_name: str
    rule_description: str
    priority: int
    action: str  # 规则动作：reject 或 approve
    hit_conditions: List[Dict[str, Any]]  # 命中的条件详情


@dataclass
class AuditResult:
    """审核结果"""
    creative_id: int
    request_id: str
    is_hit: bool  # 是否命中任何规则
    final_decision: str  # 最终决策：approve 或 reject
    hit_rules: List[RuleHitInfo]  # 命中的规则列表
    audit_time: datetime
    total_rules_checked: int  # 检查的规则总数

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'creative_id': self.creative_id,
            'request_id': self.request_id,
            'is_hit': self.is_hit,
            'final_decision': self.final_decision,
            'hit_rules': [
                {
                    'rule_id': rule.rule_id,
                    'rule_name': rule.rule_name,
                    'rule_description': rule.rule_description,
                    'priority': rule.priority,
                    'action': rule.action,
                    'hit_conditions': rule.hit_conditions
                }
                for rule in self.hit_rules
            ],
            'audit_time': self.audit_time.isoformat(),
            'total_rules_checked': self.total_rules_checked
        }


class RuleEngine:
    """规则引擎处理器"""

    def __init__(self):
        """初始化规则引擎"""
        self.db_manager = db_manager
        self._field_mapping = {
            FieldType.TITLE: 'title',
            FieldType.SUBTITLE: 'subtitle',
            FieldType.OCR_CONTENT: 'image_ocr',
            FieldType.LANDING_URL: 'ldp_url',
            FieldType.ADVERTISER_ID: 'advertiser_id',
            FieldType.INDUSTRY: 'industry'
        }

    def audit_creative(self, creative_info: CreativeInfo) -> AuditResult:
        """
        审核创意信息

        Args:
            creative_info: 创意信息对象

        Returns:
            AuditResult: 审核结果
        """
        logger.info(f"开始审核创意 {creative_info.creative_id}, 请求ID: {creative_info.request_id}")

        # 获取所有启用的规则
        active_rules = self.db_manager.get_active_rules()
        hit_rules = []

        # 逐个检查规则
        for rule in active_rules:
            rule_hit_info = self._check_rule(creative_info, rule)
            if rule_hit_info:
                hit_rules.append(rule_hit_info)
                logger.info(f"规则命中: {rule['rule_name']} (ID: {rule['id']}, 动作: {rule['action']})")

        # 根据规则优先级和动作确定最终决策
        final_decision = self._determine_final_decision(hit_rules)

        # 构建审核结果
        result = AuditResult(
            creative_id=creative_info.creative_id,
            request_id=creative_info.request_id,
            is_hit=len(hit_rules) > 0,
            final_decision=final_decision,
            hit_rules=hit_rules,
            audit_time=datetime.now(),
            total_rules_checked=len(active_rules)
        )

        logger.info(f"审核完成: 创意 {creative_info.creative_id}, 命中规则数: {len(hit_rules)}")
        return result

    def _check_rule(self, creative_info: CreativeInfo, rule: Dict[str, Any]) -> Optional[RuleHitInfo]:
        """
        检查单个规则是否命中

        Args:
            creative_info: 创意信息
            rule: 规则信息

        Returns:
            RuleHitInfo: 如果命中返回规则命中信息，否则返回None
        """
        try:
            # 解析逻辑表达式
            logic_expression = rule['logic_expression']

            # 获取表达式中涉及的所有条件ID
            condition_ids = self._extract_condition_ids(logic_expression)

            # 获取条件详情并评估
            condition_results = {}
            hit_conditions = []

            for condition_id in condition_ids:
                condition = self.db_manager.get_condition(condition_id)
                if condition:
                    is_hit, hit_detail = self._evaluate_condition(creative_info, condition)
                    condition_results[str(condition_id)] = is_hit
                    if is_hit:
                        hit_conditions.append(hit_detail)
                else:
                    logger.warning(f"条件 {condition_id} 不存在")
                    condition_results[str(condition_id)] = False

            # 评估逻辑表达式
            is_rule_hit = self._evaluate_logic_expression(logic_expression, condition_results)

            if is_rule_hit:
                return RuleHitInfo(
                    rule_id=rule['id'],
                    rule_name=rule['rule_name'],
                    rule_description=rule['rule_description'] or '',
                    priority=rule['priority'],
                    action=rule['action'],
                    hit_conditions=hit_conditions
                )

            return None

        except Exception as e:
            logger.error(f"检查规则 {rule['id']} 时发生错误: {e}")
            return None

    def _extract_condition_ids(self, logic_expression: str) -> List[int]:
        """
        从逻辑表达式中提取条件ID

        Args:
            logic_expression: 逻辑表达式，如 "(1 AND 2) OR (3 AND 4)"

        Returns:
            List[int]: 条件ID列表
        """
        import re
        # 使用正则表达式提取数字
        condition_ids = re.findall(r'\b\d+\b', logic_expression)
        return [int(cid) for cid in condition_ids]

    def _evaluate_condition(self, creative_info: CreativeInfo, condition: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        评估单个条件是否命中

        Args:
            creative_info: 创意信息
            condition: 条件信息

        Returns:
            Tuple[bool, Dict]: (是否命中, 命中详情)
        """
        try:
            field_type = FieldType(condition['field_name'])
            operator = OperatorType(condition['operator'])

            # 获取创意信息中对应字段的值
            field_attr = self._field_mapping.get(field_type)
            if not field_attr:
                logger.warning(f"不支持的字段类型: {field_type}")
                return False, {}

            field_value = getattr(creative_info, field_attr, None)
            if field_value is None:
                field_value = ""

            # 转换为字符串进行处理
            field_value = str(field_value)

            # 构建命中详情
            hit_detail = {
                'condition_id': condition['id'],
                'condition_name': condition['condition_name'],
                'field_name': condition['field_name'],
                'operator': condition['operator'],
                'field_value': field_value,
                'check_value': condition.get('value') or condition.get('wordlist_name', ''),
                'hit_time': datetime.now().isoformat()
            }

            # 根据操作类型进行判断
            if operator == OperatorType.CONTAINS:
                is_hit = condition['value'] in field_value
            elif operator == OperatorType.NOT_CONTAINS:
                is_hit = condition['value'] not in field_value
            elif operator == OperatorType.EQUALS:
                is_hit = field_value == condition['value']
            elif operator == OperatorType.NOT_EQUALS:
                is_hit = field_value != condition['value']
            elif operator == OperatorType.IN_WORDLIST:
                is_hit = self._check_wordlist_match(field_value, condition['wordlist_id'], True)
            elif operator == OperatorType.NOT_IN_WORDLIST:
                is_hit = self._check_wordlist_match(field_value, condition['wordlist_id'], False)
            else:
                logger.warning(f"不支持的操作类型: {operator}")
                is_hit = False

            return is_hit, hit_detail

        except Exception as e:
            logger.error(f"评估条件 {condition['id']} 时发生错误: {e}")
            return False, {}

    def _check_wordlist_match(self, field_value: str, wordlist_id: int, should_match: bool) -> bool:
        """
        检查字段值是否与词表匹配

        Args:
            field_value: 字段值
            wordlist_id: 词表ID
            should_match: True表示应该匹配(in_wordlist)，False表示不应该匹配(not_in_wordlist)

        Returns:
            bool: 是否符合条件
        """
        try:
            if not wordlist_id:
                return False

            # 获取词表中的所有词汇
            words = self.db_manager.get_wordlist(wordlist_id)

            # 检查是否有任何词汇在字段值中
            has_match = any(word in field_value for word in words if word)

            # 根据should_match返回结果
            return has_match if should_match else not has_match

        except Exception as e:
            logger.error(f"检查词表 {wordlist_id} 匹配时发生错误: {e}")
            return False

    def _evaluate_logic_expression(self, expression: str, condition_results: Dict[str, bool]) -> bool:
        """
        评估逻辑表达式

        Args:
            expression: 逻辑表达式，如 "(1 AND 2) OR (3 AND 4)"
            condition_results: 条件ID到结果的映射

        Returns:
            bool: 表达式评估结果
        """
        try:
            # 替换表达式中的条件ID为对应的布尔值
            eval_expression = expression
            for condition_id, result in condition_results.items():
                eval_expression = eval_expression.replace(condition_id, str(result))

            # 替换逻辑操作符
            eval_expression = eval_expression.replace('AND', 'and')
            eval_expression = eval_expression.replace('OR', 'or')
            eval_expression = eval_expression.replace('NOT', 'not')

            # 安全评估表达式
            # 只允许布尔值、逻辑操作符和括号
            allowed_chars = set('TrueFalse()andornot ')
            if not all(c in allowed_chars for c in eval_expression.replace(' ', '')):
                logger.error(f"逻辑表达式包含不安全字符: {expression}")
                return False

            result = eval(eval_expression)
            return bool(result)

        except Exception as e:
            logger.error(f"评估逻辑表达式 '{expression}' 时发生错误: {e}")
            return False

    def _determine_final_decision(self, hit_rules: List[RuleHitInfo]) -> str:
        """
        根据命中的规则确定最终决策

        规则：
        1. 如果没有命中任何规则，默认通过
        2. 按优先级从高到低处理规则
        3. 相同优先级的情况下，如果有通过规则，则忽略拒绝规则
        4. 如果最高优先级只有拒绝规则，则拒绝

        Args:
            hit_rules: 命中的规则列表

        Returns:
            str: 最终决策 "approve" 或 "reject"
        """
        if not hit_rules:
            return "approve"  # 没有命中任何规则，默认通过

        # 按优先级分组
        priority_groups = {}
        for rule in hit_rules:
            priority = rule.priority
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(rule)

        # 按优先级从高到低处理
        for priority in sorted(priority_groups.keys(), reverse=True):
            rules_at_priority = priority_groups[priority]

            # 检查是否有通过规则
            has_approve = any(rule.action == "approve" for rule in rules_at_priority)
            has_reject = any(rule.action == "reject" for rule in rules_at_priority)

            if has_approve:
                logger.info(f"优先级 {priority} 有通过规则，最终决策：通过")
                return "approve"
            elif has_reject:
                logger.info(f"优先级 {priority} 只有拒绝规则，最终决策：拒绝")
                return "reject"

        # 理论上不会到达这里，但作为保险
        return "reject"


# 创建全局规则引擎实例
rule_engine = RuleEngine()


def audit_creative_info(creative_info: CreativeInfo) -> AuditResult:
    """
    审核创意信息的便捷函数

    Args:
        creative_info: 创意信息对象

    Returns:
        AuditResult: 审核结果
    """
    return rule_engine.audit_creative(creative_info)